const uiData = require('../static/lang.ui.json')
const cardData = require('../static/lang.card_meta.json')

module.exports = {
  // 基本信息
  language: {
    name: 'English',
    nativeName: 'English',
    code: 'en',
    iso: 'en-US'
  },
  
  // SEO元数据
  seo: uiData.en.seo,
  
  // UI文本
  ui: uiData.en,
  
  // 卡片元数据
  card: cardData.en,
  
  // 页面特定的SEO数据
  pages: {
    index: {
      title: 'Yu-Gi-Oh Card Maker - Free Online Card Design Tool',
      description: 'Professional card creation tool for Yu-Gi-Oh enthusiasts. Design custom monsters, spells, and trap cards with advanced features. Free to use with high-quality downloads.',
      keywords: 'Yu-Gi-Oh card maker,card design,card creator,yugioh,free tool,online maker,monster cards,spell cards,trap cards,pendulum,link monsters'
    },
    privacy: {
      title: 'Privacy Policy',
      description: 'Privacy policy for Yu-Gi-Oh Card Maker - how we handle your data and protect your privacy.',
      keywords: 'privacy policy,data protection,user privacy,Yu-Gi-Oh card maker'
    },
    terms: {
      title: 'Terms of Service',
      description: 'Terms of service for Yu-Gi-Oh Card Maker - rules and guidelines for using our service.',
      keywords: 'terms of service,user agreement,service terms,Yu-Gi-Oh card maker'
    }
  },
  
  // 导航
  navigation: {
    home: 'Home',
    privacy: 'Privacy',
    terms: 'Terms'
  },
  
  // 通用文本
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    close: 'Close',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    language: 'Language'
  }
}

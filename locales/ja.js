const uiData = require('../static/lang.ui.json')
const cardData = require('../static/lang.card_meta.json')

module.exports = {
  // 基本信息
  language: {
    name: '日本語',
    nativeName: '日本語',
    code: 'ja',
    iso: 'ja-JP'
  },
  
  // SEO元数据
  seo: uiData.ja.seo,

  // UI文本
  ui: uiData.ja,

  // 卡片元数据
  card: cardData.ja,
  
  // 页面特定的SEO数据
  pages: {
    index: {
      title: '遊戯王カードメーカー - 無料オンラインカード作成ツール',
      description: 'プロフェッショナルな遊戯王カード作成ツール。モンスター、魔法、罠カードなど全タイプに対応。無料で使用でき、ペンデュラム、リンクモンスター対応、高品質画像ダウンロード。',
      keywords: '遊戯王カードメーカー,カード作成,カードデザイン,遊戯王,無料ツール,オンライン作成,モンスターカード,魔法カード,罠カード,ペンデュラム,リンクモンスター'
    },
    privacy: {
      title: 'プライバシーポリシー',
      description: '遊戯王カードメーカーのプライバシーポリシー - データの取り扱いとプライバシー保護について。',
      keywords: 'プライバシーポリシー,データ保護,ユーザープライバシー,遊戯王カードメーカー'
    },
    terms: {
      title: '利用規約',
      description: '遊戯王カードメーカーの利用規約 - サービス利用のルールとガイドライン。',
      keywords: '利用規約,ユーザー契約,サービス条件,遊戯王カードメーカー'
    }
  },
  
  // 导航
  navigation: {
    home: 'ホーム',
    privacy: 'プライバシー',
    terms: '利用規約'
  },
  
  // 通用文本
  common: {
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
    close: '閉じる',
    save: '保存',
    cancel: 'キャンセル',
    confirm: '確認',
    back: '戻る',
    next: '次へ',
    previous: '前へ',
    language: '言語'
  }
}

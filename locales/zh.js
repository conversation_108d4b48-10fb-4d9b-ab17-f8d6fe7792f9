const uiData = require('../static/lang.ui.json')
const cardData = require('../static/lang.card_meta.json')

module.exports = {
  // 基本信息
  language: {
    name: '中文',
    nativeName: '中文',
    code: 'zh',
    iso: 'zh-TW'
  },
  
  // SEO元数据
  seo: uiData.zh.seo,
  
  // UI文本
  ui: uiData.zh,
  
  // 卡片元数据
  card: cardData.zh,
  
  // 页面特定的SEO数据
  pages: {
    index: {
      title: '遊戲王卡片製造機 - 免費線上卡片設計工具',
      description: '專業的遊戲王卡片製作工具，支持怪獸、魔法、陷阱等所有卡片類型。免費使用，支持靈擺、連結怪獸，提供高品質圖片下載。',
      keywords: '遊戲王卡片製造機,卡片製作,卡片設計,遊戲王,免費工具,線上製作,怪獸卡,魔法卡,陷阱卡,靈擺,連結怪獸'
    },
    privacy: {
      title: '隱私政策',
      description: '遊戲王卡片製造機的隱私政策 - 我們如何處理您的數據並保護您的隱私。',
      keywords: '隱私政策,數據保護,用戶隱私,遊戲王卡片製造機'
    },
    terms: {
      title: '服務條款',
      description: '遊戲王卡片製造機的服務條款 - 使用我們服務的規則和指導原則。',
      keywords: '服務條款,用戶協議,服務條件,遊戲王卡片製造機'
    }
  },
  
  // 导航
  navigation: {
    home: '首頁',
    privacy: '隱私政策',
    terms: '服務條款'
  },
  
  // 通用文本
  common: {
    loading: '載入中...',
    error: '錯誤',
    success: '成功',
    close: '關閉',
    save: '保存',
    cancel: '取消',
    confirm: '確認',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    language: '語言'
  }
}

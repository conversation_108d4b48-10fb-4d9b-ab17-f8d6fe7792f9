<template>
  <div class="language-switcher">
    <b-dropdown 
      :text="currentLanguageDisplay" 
      variant="outline-light" 
      size="sm"
      right
      class="language-dropdown"
    >
      <template #button-content>
        <fa :icon="['fas', 'globe']" class="me-2" />
        {{ currentLanguageDisplay }}
      </template>
      
      <b-dropdown-item
        v-for="lang in sortedLanguages"
        :key="lang.code"
        :active="currentLanguage === lang.code"
        @click="switchLanguage(lang.code)"
        class="language-option"
      >
        {{ lang.name }}
      </b-dropdown-item>
    </b-dropdown>
  </div>
</template>

<script>
export default {
  name: 'LanguageSwitcher',

  computed: {
    currentLanguageDisplay() {
      const current = this.$i18n.locales.find(locale => locale.code === this.$i18n.locale)
      return current ? current.name : 'Language'
    },

    sortedLanguages() {
      // 按照指定顺序排序：英文、中文、日文
      const order = ['en', 'zh', 'ja']
      return [...this.$i18n.locales].sort((a, b) => {
        return order.indexOf(a.code) - order.indexOf(b.code)
      })
    },

    currentLanguage() {
      return this.$i18n.locale
    }
  },
  
  methods: {
    /**
     * 切換語言 - 使用 i18n 路由跳转
     * @param {string} langCode - 語言代碼 (en, zh, ja)
     */
    switchLanguage(langCode) {
      if (langCode !== this.currentLanguage) {
        try {
          // 使用 switchLocalePath 方法切换到对应语言的路径
          const targetPath = this.switchLocalePath(langCode)

          // 跳转到对应语言的URL
          this.$router.push(targetPath)

          // 觸發全局語言切換事件（保持向后兼容）
          this.$emit('language-changed', langCode)

          // 追踪语言切换事件
          this.trackLanguageSwitch(langCode)
        } catch (error) {
          console.error('Language switch error:', error)

          // 降级方案：直接跳转到对应语言的首页
          const fallbackPath = langCode === 'en' ? '/' : `/${langCode}`
          this.$router.push(fallbackPath)
        }
      }
    },

    /**
     * 追踪语言切换事件
     * @param {string} langCode - 新的语言代码
     */
    trackLanguageSwitch(langCode) {
      try {
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'language_switch', {
            event_category: 'engagement',
            event_label: `switch_to_${langCode}`,
            value: 1
          })
        }
      } catch (e) {
        // Ignore analytics errors
      }
    }
  }
}
</script>

<style scoped>
.language-switcher {
  position: relative;
}

.language-dropdown >>> .btn {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.language-dropdown >>> .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.language-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.language-option:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.language-option.active {
  background-color: rgba(0, 123, 255, 0.2);
  font-weight: 600;
}



/* 響應式設計 */
@media (max-width: 768px) {
  .language-dropdown >>> .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* 無障礙設計 */
.language-option:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .language-dropdown >>> .btn {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
  }
  
  .language-dropdown >>> .btn:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>

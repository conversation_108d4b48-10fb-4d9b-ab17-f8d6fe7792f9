{"version": 2, "buildCommand": "npm run vercel-build", "outputDirectory": "dist", "installCommand": "npm ci", "framework": null, "headers": [{"source": "/fonts/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_nuxt/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/((?!sitemap\\.xml|robots\\.txt|favicon\\.ico|.*\\.(png|jpg|jpeg|gif|svg|webp|css|js|woff|woff2|ttf|otf|eot)).*)", "destination": "/index.html"}]}
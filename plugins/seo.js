export default function ({ app, route, $i18n }) {
  // 只在客户端运行
  if (process.client) {
    return
  }

  // 获取当前语言的SEO数据
  const getSEOData = (pageName = 'index') => {
    const locale = $i18n.locale
    const localeData = $i18n.messages[locale]
    
    if (!localeData) {
      return null
    }

    // 获取页面特定的SEO数据
    const pageSEO = localeData.pages && localeData.pages[pageName]
    const globalSEO = localeData.seo

    return {
      ...globalSEO,
      ...pageSEO
    }
  }

  // 生成hreflang链接
  const generateHreflangLinks = (routeName) => {
    const links = []
    const locales = $i18n.locales

    locales.forEach(locale => {
      const localePath = app.localePath({ name: routeName }, locale.code)
      links.push({
        rel: 'alternate',
        hreflang: locale.iso,
        href: `${$i18n.baseUrl}${localePath}`
      })
    })

    // 添加x-default
    const defaultPath = app.localePath({ name: routeName }, $i18n.defaultLocale)
    links.push({
      rel: 'alternate',
      hreflang: 'x-default',
      href: `${$i18n.baseUrl}${defaultPath}`
    })

    return links
  }

  // 生成canonical链接
  const generateCanonicalLink = (routeName) => {
    const currentPath = app.localePath({ name: routeName }, $i18n.locale)
    return {
      rel: 'canonical',
      href: `${$i18n.baseUrl}${currentPath}`
    }
  }

  // 生成结构化数据
  const generateStructuredData = (seoData, routeName) => {
    if (!seoData || !seoData.structuredData) {
      return null
    }

    const baseStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebApplication',
      url: `${$i18n.baseUrl}${app.localePath({ name: routeName }, $i18n.locale)}`,
      ...seoData.structuredData
    }

    // 添加多语言版本
    const alternateLanguages = $i18n.locales.map(locale => ({
      '@type': 'Language',
      name: locale.name,
      alternateName: locale.iso,
      url: `${$i18n.baseUrl}${app.localePath({ name: routeName }, locale.code)}`
    }))

    baseStructuredData.inLanguage = alternateLanguages

    return baseStructuredData
  }

  // 导出工具函数供页面使用
  app.$seo = {
    getSEOData,
    generateHreflangLinks,
    generateCanonicalLink,
    generateStructuredData
  }
}

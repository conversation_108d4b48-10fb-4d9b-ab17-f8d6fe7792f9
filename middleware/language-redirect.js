export default function ({ app, route, redirect, $i18n }) {
  // 只在服务端运行，避免客户端重复重定向
  if (process.client) {
    return
  }

  // 如果已经在特定语言路径下，不需要重定向
  if (route.name && route.name.includes('___')) {
    return
  }

  // 获取浏览器语言偏好
  const getBrowserLanguage = (req) => {
    if (!req || !req.headers) {
      return null
    }

    const acceptLanguage = req.headers['accept-language']
    if (!acceptLanguage) {
      return null
    }

    // 解析 Accept-Language 头
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const parts = lang.trim().split(';')
        const code = parts[0]
        const quality = parts[1] ? parseFloat(parts[1].split('=')[1]) : 1.0
        return { code, quality }
      })
      .sort((a, b) => b.quality - a.quality)

    return languages
  }

  // 匹配支持的语言
  const matchSupportedLanguage = (browserLanguages, supportedLocales) => {
    for (const browserLang of browserLanguages) {
      const langCode = browserLang.code.toLowerCase()
      
      // 精确匹配
      for (const locale of supportedLocales) {
        if (locale.iso.toLowerCase() === langCode || locale.code === langCode) {
          return locale.code
        }
      }
      
      // 语言前缀匹配
      const langPrefix = langCode.split('-')[0]
      for (const locale of supportedLocales) {
        const localePrefix = locale.iso.toLowerCase().split('-')[0]
        if (localePrefix === langPrefix) {
          return locale.code
        }
      }
    }
    
    return null
  }

  // 检查是否需要重定向
  const shouldRedirect = () => {
    // 如果是根路径且没有语言前缀
    if (route.path === '/') {
      const browserLanguages = getBrowserLanguage(app.context.req)
      if (!browserLanguages || browserLanguages.length === 0) {
        return null
      }

      const matchedLanguage = matchSupportedLanguage(browserLanguages, $i18n.locales)
      
      // 如果匹配到的语言不是默认语言，则重定向
      if (matchedLanguage && matchedLanguage !== $i18n.defaultLocale) {
        return matchedLanguage
      }
    }
    
    return null
  }

  // 执行重定向
  const targetLanguage = shouldRedirect()
  if (targetLanguage) {
    const localePath = app.localePath(route.path, targetLanguage)
    
    // 记录重定向事件（用于分析）
    if (process.env.NODE_ENV === 'production') {
      console.log(`Language redirect: ${route.path} -> ${localePath} (${targetLanguage})`)
    }
    
    return redirect(localePath)
  }
}

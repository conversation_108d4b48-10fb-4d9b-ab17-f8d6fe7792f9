export default {
  // 支持的语言列表
  locales: [
    {
      code: 'en',
      iso: 'en-US',
      name: 'English',
      file: 'en.js',
      dir: 'ltr'
    },
    {
      code: 'zh',
      iso: 'zh-TW',
      name: '中文',
      file: 'zh.js',
      dir: 'ltr'
    },
    {
      code: 'ja',
      iso: 'ja-JP',
      name: '日本語',
      file: 'ja.js',
      dir: 'ltr'
    }
  ],
  
  // 默认语言
  defaultLocale: 'en',
  
  // 语言文件目录
  langDir: 'locales/',
  
  // 懒加载语言文件
  lazy: true,
  
  // URL策略
  strategy: 'prefix_except_default',
  
  // 检测浏览器语言
  detectBrowserLanguage: {
    useCookie: true,
    cookieKey: 'i18n_redirected',
    redirectOn: 'root',
    alwaysRedirect: false,
    fallbackLocale: 'en'
  },
  
  // SEO配置
  seo: true,
  
  // 基础URL
  baseUrl: 'https://yugiohcardmaker.org',
  
  // 页面配置
  pages: {
    'index': {
      en: '/',
      zh: '/zh',
      ja: '/ja'
    },
    'privacy': {
      en: '/privacy',
      zh: '/zh/privacy',
      ja: '/ja/privacy'
    },
    'terms': {
      en: '/terms',
      zh: '/zh/terms',
      ja: '/ja/terms'
    }
  },
  
  // 跳过默认语言前缀
  skipSettingLocaleOnNavigate: false,
  
  // 自定义路由
  customRoutes: 'config',
  
  // 解析页面
  parsePages: false,
  
  // 编码
  encodePaths: false
}

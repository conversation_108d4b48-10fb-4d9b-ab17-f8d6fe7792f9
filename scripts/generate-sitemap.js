/**
 * Advanced Sitemap Generator for Yu-Gi-Oh! Card Maker
 * Generates XML sitemap with multi-language support and proper formatting
 */

const fs = require('fs')
const path = require('path')

// Configuration
const config = {
  hostname: 'https://yugiohcardmaker.org',
  languages: ['en', 'zh', 'ja'],  // Fixed order: en is default, ja instead of jp
  defaultLanguage: 'en',          // Fixed: en is the default language
  outputPath: path.join(__dirname, '../static/sitemap.xml')
}

// Page definitions with metadata
const pages = [
  {
    path: '/',
    priority: 1.0,
    changefreq: 'weekly',
    multilang: true,
    description: 'Yu-Gi-Oh! Card Maker - Main page'
  }
  // Removed privacy and terms pages as requested - they should not appear in sitemap
]

/**
 * Generate XML sitemap with proper formatting
 */
function generateSitemap() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n'
  xml += '        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n'

  // Generate URLs for each language
  config.languages.forEach(lang => {
    pages.forEach(page => {
      // Generate URL based on language and path
      let url
      if (lang === config.defaultLanguage) {
        // Default language (en) - no prefix
        url = page.path === '/' ? '/' : page.path
      } else {
        // Other languages - use prefix
        url = page.path === '/' ? `/${lang}` : `/${lang}${page.path}`
      }

      xml += '  <url>\n'
      xml += `    <loc>${config.hostname}${url}</loc>\n`
      xml += `    <lastmod>${currentDate}</lastmod>\n`
      xml += `    <changefreq>${page.changefreq}</changefreq>\n`
      xml += `    <priority>${page.priority}</priority>\n`

      // Add alternate language links if multilingual
      if (page.multilang) {
        config.languages.forEach(altLang => {
          let altUrl
          if (altLang === config.defaultLanguage) {
            altUrl = page.path === '/' ? '/' : page.path
          } else {
            altUrl = page.path === '/' ? `/${altLang}` : `/${altLang}${page.path}`
          }

          xml += `    <xhtml:link rel="alternate" hreflang="${altLang}" href="${config.hostname}${altUrl}" />\n`
        })

        // Add x-default for international targeting (use default language)
        const defaultUrl = page.path === '/' ? '/' : page.path
        xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${config.hostname}${defaultUrl}" />\n`
      }

      xml += '  </url>\n'
    })
  })

  xml += '</urlset>\n'
  
  return xml
}

/**
 * Generate sitemap index for multiple sitemaps (future expansion)
 */
function generateSitemapIndex() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
  xml += '  <sitemap>\n'
  xml += `    <loc>${config.hostname}/sitemap.xml</loc>\n`
  xml += `    <lastmod>${currentDate}</lastmod>\n`
  xml += '  </sitemap>\n'
  xml += '</sitemapindex>\n'
  
  return xml
}

/**
 * Write sitemap to file
 */
function writeSitemap() {
  try {
    const sitemapXml = generateSitemap()
    
    // Ensure directory exists
    const dir = path.dirname(config.outputPath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    
    // Write main sitemap
    fs.writeFileSync(config.outputPath, sitemapXml, 'utf8')
    console.log(`✅ Sitemap generated successfully: ${config.outputPath}`)
    
    // Generate sitemap index (for future use)
    const indexPath = path.join(__dirname, '../static/sitemap-index.xml')
    const indexXml = generateSitemapIndex()
    fs.writeFileSync(indexPath, indexXml, 'utf8')
    console.log(`✅ Sitemap index generated: ${indexPath}`)
    
    // Log sitemap contents
    console.log('\n📋 Sitemap Contents:')
    config.languages.forEach(lang => {
      pages.forEach(page => {
        let url
        if (lang === config.defaultLanguage) {
          url = page.path === '/' ? '/' : page.path
        } else {
          url = page.path === '/' ? `/${lang}` : `/${lang}${page.path}`
        }
        console.log(`   ${config.hostname}${url} (Priority: ${page.priority}, Changefreq: ${page.changefreq}, Lang: ${lang})`)
      })
    })

    console.log(`\n📊 Total URLs: ${pages.length * config.languages.length}`)
    console.log(`🌐 Languages: ${config.languages.join(', ')}`)
    console.log(`🔗 Hostname: ${config.hostname}`)
    console.log(`📄 Pages per language: ${pages.length}`)
    
  } catch (error) {
    console.error('❌ Error generating sitemap:', error)
    process.exit(1)
  }
}

// Generate sitemap if run directly
if (require.main === module) {
  writeSitemap()
}

module.exports = {
  generateSitemap,
  generateSitemapIndex,
  writeSitemap,
  config,
  pages
}
